import React from 'react'
import { useThemeStore } from '../store/useThemeStore.js';
import { THEMES } from '../constants/index.js';
import { Send } from 'lucide-react';

const PREVIEW_MESSAGES=[
  {id:1, content:"hey how are you?"},
  {id:2, message:"Hi"},
]

const SettingsPage = () => {
  const {theme, setTheme} = useThemeStore();
  return (
    <div className="h-screen container pt-20 mx-auto px-4 max-w-5xl">
      <div className="space-y-6">
        <div className="flex flex-col gap-1">
          <h2 className="text-lg font-semibold">Theme</h2>
          <p className="text-sm text-base-content/70">
            Choose a theme for your chat interface
          </p>
        </div>

        <div className="grid grid-cols-4 sm:grid-cols-6 md:grid-cols-8 gap-2">
          {THEMES.map((t) => (
            <button
              key={t}
              onClick={() => setTheme(t)}
              className={`rounded-lg p-2 group flex flex-col items-center gap-1.5 transition-colors ${
                theme === t ? "bg-base-200" : "hover:bg-base-200/50"
              }`}
            >
              <div
                className="relative h-8 w-full rounded-md overflow-hidden"
                data-theme={t}
              >
                <div className="absolute inset-0 grid grid-cols-4 gap-px p-1">
                  <div className="rounded bg-primary"></div>
                  <div className="rounded bg-secondary"></div>
                  <div className="rounded bg-accent"></div>
                  <div className="rounded bg-neutral"></div>
                </div>
              </div>
              <span className="text-[11px] font-medium truncate w-full">
                {t.charAt(0).toUpperCase() + t.slice(1)}
              </span>
            </button>
          ))}
        </div>

        {/*Preview*/}
        <h3 className="text-lg font-semibold mb-3">Preview</h3>
        <div className="rounded-xl border border-base-300 overflow-hidden bg-base-100 shadow-lg">
          <div className="p-4 bg-base-200">
            <div className="max-w-lg mx-auto">
              {/*Mock UI*/}
              <div className="bg-base-100 rounded-xl shadow-sm overflow-hidden">
                <div className="px-4 py-3 bg-base-100 border-b border-base-300">
                  <div className="flex  items-center gap-3">
                    <div className="size-10 rounded-full bg-primary flex items-center justify-center text-primary-content font-medium">
                      R
                    </div>
                    <div>
                      <h2 className="text-sm font-medium">Rudrangshu</h2>
                      <p className="text-xs text-base-content/70">Online</p>
                    </div>
                  </div>
                </div>
                {/*chat*/}
                <div className="p-4 space-y-4 min-h-[200px]  max-h-[200px] overflow-y-auto bg-base-100">
                  {PREVIEW_MESSAGES.map((message) => (
                    <div
                      key={message.id}
                      className={`flex ${
                        message.isSent ?"justify-end" : "justify-start"}`}
                    >
                      <div
                        className={`max-w-[80%] p-3 rounded-xl shadow-sm ${
                          message.isSent ? "bg-primary text-primary-content" : "bg-base-200" 
                          }`}
                      >
                       <p className="text-sm">{message.content}</p> 
                       <p className={`text-[10px] mt-1.5 ${
                          message.isSent ? "text-primary-content/70" : "text-base-content/70" 
                          }`}>
                          12.00 PM
                       </p>
                      </div>
                    </div>
                  ))}
                </div>

                <div className="p-4 bg-base-100 border-t border-base-300">
                 <div className="flex gap-2">
                  <input
                    type="text"
                    placeholder="Type a message ...."
                    className="input input-bordered flex-1 text-sm h-10"
                    value="This is a preview message"
                    readOnly
                  />
                  <button className='btn btn-primary h-10 min-h-0'>
                    <Send size={18}/>
                  </button>
                 </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default SettingsPage